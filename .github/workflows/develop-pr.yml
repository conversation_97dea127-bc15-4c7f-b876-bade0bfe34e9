name: docker develop build and test only

on:
  pull_request:
    branches:
      - main
      - release-*
      - verkle
      - performance

jobs:
  hadolint:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - name: Set up Java
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00
        with:
          distribution: temurin
          java-version: 21
      - name: setup gradle
        uses: gradle/actions/setup-gradle@9e899d11ad247ec76be7a60bc1cf9d3abbb9e7f1
        with:
          cache-disabled: true

      - name: hadoLint
        run: docker run --rm -i hadolint/hadolint < docker/Dockerfile
  buildDocker:
    needs: hadolint
    permissions:
      contents: read
      packages: write

    strategy:
      fail-fast: false
      matrix:
        platform:
          - ubuntu-22.04
          - besu-arm64
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Prepare
        id: prep
        run: |
          platform=${{ matrix.platform }}
          if [ "$platform" = 'ubuntu-22.04' ]; then 
            echo "PLATFORM_PAIR=linux-amd64" >> $GITHUB_OUTPUT
            echo "ARCH=amd64" >> $GITHUB_OUTPUT
          else
            echo "PLATFORM_PAIR=linux-arm64" >> $GITHUB_OUTPUT
            echo "ARCH=arm64" >> $GITHUB_OUTPUT
          fi

          # Get the current date and time in the format YY.MM
          DATE_TIME=$(date +"%y.%-m")
          # Get the short SHA of the merge commit
          SHORT_SHA=${GITHUB_SHA::7}
          # Construct the build target name
          BUILD_TARGET_NAME="${DATE_TIME}-develop-${SHORT_SHA}"
          echo "Build Target Name: $BUILD_TARGET_NAME"
          # Set the build target name as an environment variable
          echo "BUILD_TARGET_NAME=${BUILD_TARGET_NAME}" >> $GITHUB_ENV

      - name: Checkout Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - name: Set up Java
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00
        with:
          distribution: temurin
          java-version: 21
      - name: setup gradle
        uses: gradle/actions/setup-gradle@9e899d11ad247ec76be7a60bc1cf9d3abbb9e7f1
        with:
          cache-disabled: true
      - name: install goss
        run: |
          mkdir -p docker/reports
          curl -L https://github.com/aelsabbahy/goss/releases/download/v0.4.4/goss-${{ steps.prep.outputs.PLATFORM_PAIR }} -o ./docker/tests/goss-${{ steps.prep.outputs.PLATFORM_PAIR }}
      - name: build and test docker
        uses: gradle/actions/setup-gradle@9e899d11ad247ec76be7a60bc1cf9d3abbb9e7f1
        env:
          architecture: ${{ steps.prep.outputs.ARCH }}
        with:
          cache-disabled: true
          arguments: testDocker -PdockerOrgName=${{ env.registry }}/${{ secrets.DOCKER_ORG }} -Pversion=${{ env.BUILD_TARGET_NAME}} -Prelease.releaseVersion=develop
