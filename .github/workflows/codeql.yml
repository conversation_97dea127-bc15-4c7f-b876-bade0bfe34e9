# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
#
# ******** NOTE ********
# We have attempted to detect the languages in your repository. Please check
# the `language` matrix defined below to confirm you have the correct set of
# supported CodeQL languages.
#
name: "CodeQL"

on:
  workflow_dispatch:
  schedule:
    # * is a special character in YAML so you have to quote this string
    # expression evaluates to midnight every night
    - cron: '0 0 * * *'

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-22.04
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      fail-fast: false
      matrix:
        language: ['java']  # Add other languages as needed: 'javascript', 'python', etc.
    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - name: Set up Java
      uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00
      with:
        distribution: 'temurin'
        java-version: 21
    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@9e8d0789d4a0fa9ceb6b1738f7e269594bdd67f0
      with:
        languages: ${{ matrix.language }}
        # If you wish to specify custom queries, you can do so here or in a config file.
        # By default, queries listed here will override any specified in a config file.
        # Prefix the list here with "+" to use these queries and those in the config file.
        queries: security-and-quality,security-extended

    - name: setup gradle
      uses: gradle/actions/setup-gradle@9e899d11ad247ec76be7a60bc1cf9d3abbb9e7f1
      with:
        cache-disabled: true
    - name: compileJava noscan
      run: |
        JAVA_OPTS="-Xmx2048M" ./gradlew --no-scan compileJava
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@9e8d0789d4a0fa9ceb6b1738f7e269594bdd67f0
