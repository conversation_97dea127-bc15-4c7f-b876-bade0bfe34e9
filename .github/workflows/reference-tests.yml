name: reference-tests
on:
  workflow_dispatch:
  merge_group:
  pull_request:
    branches:
      - main
      - release-*
      - verkle
      - performance

env:
  GRADLE_OPTS: "-Xmx6g -Dorg.gradle.daemon=false -Dorg.gradle.parallel=true -Dorg.gradle.caching=true"
  total-runners: 4

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  referenceTestEthereum:
    runs-on: ubuntu-22.04
    permissions:
      statuses: write
      checks: write
      packages: read
    strategy:
      fail-fast: true
      matrix:
        runner_index: [1,2,3,4]
    steps:
      - name: Checkout Rep<PERSON>
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          ref: ${{ github.event.pull_request.head.sha || github.ref }}
          submodules: recursive
      - name: Set up Java
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00
        with:
          distribution: semeru # IBM Semeru with OpenJ9
          java-version: 21
      - name: setup gradle
        uses: gradle/actions/setup-gradle@9e899d11ad247ec76be7a60bc1cf9d3abbb9e7f1
        with:
          cache-disabled: true
      - name: execute generate reference tests
        run: ./gradlew ethereum:referencetests:referenceTestClasses -Dorg.gradle.parallel=true -Dorg.gradle.caching=true
      - name: list test files generated
        run: find ethereum/referencetests/build/generated/sources/reference-test -name "*.java" | sort >> filenames.txt
      - name: list test files written
        run: find ethereum/referencetests/src/reference-test/java -name "*.java" | sort >> filenames.txt
      - name: Split tests
        run: ./.github/workflows/splitList.sh filenames.txt ${{env.total-runners}}
      - name: echo test file count
        run: cat group_${{matrix.runner_index}}.txt | wc
      - name: convert to test suite classnames
        run: cat group_${{matrix.runner_index}}.txt | sed -e 's/^.*java\///' -e 's@/@.@g' -e 's/\.java//' -e 's/^/--tests /' > testClasses.txt
      - name: compose gradle args
        run: tr '\n' ' ' < testClasses.txt > refTestArgs.txt
      - name: refTestArgs.txt
        run: cat refTestArgs.txt
      - name: run reference tests
        run: ./gradlew ethereum:referenceTests:referenceTests `cat refTestArgs.txt`
      - name: Upload Test Report
        uses: actions/upload-artifact@5d5d22a31266ced268874388b861e4b58bb5c2f3
        if: always() # always run even if the previous step fails
        with:
          name: reference-test-node-${{matrix.runner_index}}-results
          path: '**/build/test-results/referenceTests/TEST-*.xml'
  reftests-passed:
    name: "reftests-passed"
    runs-on: ubuntu-22.04
    needs: [ referenceTestEthereum ]
    permissions:
      checks: write
      statuses: write
    if: always()
    steps:
      # Fail if any `needs` job was not a success.
      # Along with `if: always()`, this allows this job to act as a single required status check for the entire workflow.
      - name: Fail on workflow error
        run: exit 1
        if: >-
          ${{
            contains(needs.*.result, 'failure')
            || contains(needs.*.result, 'cancelled')
            || contains(needs.*.result, 'skipped')
          }}
