name: Docker Promote

run-name: "Docker Promote ${{ github.event.release.name }}"

on:
  release:
    types: [released]

env:
  registry: docker.io
  GRADLE_OPTS: "-Dorg.gradle.parallel=true -Dorg.gradle.caching=true"

jobs:
  validate:
    runs-on: ubuntu-22.04
    env:
      RELEASE_VERSION: "${{ github.event.release.name }}"
    steps:
      - name: Pre-process Release Name
        id: pre_process_release_version
        run: |       
          # strip all whitespace
          RELEASE_VERSION="${RELEASE_VERSION//[[:space:]]/}"
          if [[ ! "$RELEASE_VERSION" =~ ^[0-9]+\.[0-9]+(\.[0-9]+)?(-.*)?$ ]]; then
            echo "Release name does not conform to a valid besu release format YY.M.v[-suffix], e.g. 24.8.0-RC1."
            exit 1
          fi
          echo "release_version=$RELEASE_VERSION" >> $GITHUB_OUTPUT  # Set as output using the new syntax
    outputs:
      release_version: ${{ steps.pre_process_release_version.outputs.release_version }}

  docker-promote:
    needs: [validate]
    env:
      RELEASE_VERSION: ${{ needs.validate.outputs.release_version }}
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Setup Java
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00
        with:
          distribution: temurin
          java-version: 21
          cache: gradle

      - name: Login to ${{ env.registry }}
        uses: docker/login-action@343f7c4344506bcbf9b4de18042ae17996df046d
        with:
          registry: ${{ env.registry }}
          username: ${{ secrets.DOCKER_USER_RW }}
          password: ${{ secrets.DOCKER_PASSWORD_RW }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@9e899d11ad247ec76be7a60bc1cf9d3abbb9e7f1
        with:
          cache-disabled: true

      - name: Docker upload
        run: ./gradlew "-Prelease.releaseVersion=${{ env.RELEASE_VERSION }}" "-PdockerOrgName=${{ env.registry }}/${{ secrets.DOCKER_ORG }}" dockerUploadRelease

      - name: Docker manifest
        run: ./gradlew "-Prelease.releaseVersion=${{ env.RELEASE_VERSION }}" "-PdockerOrgName=${{ env.registry }}/${{ secrets.DOCKER_ORG }}" manifestDockerRelease

  docker-verify:
    needs: [validate,docker-promote]
    env:
      CONTAINER_NAME: besu-check
      RELEASE_VERSION: ${{ needs.validate.outputs.release_version }}
    runs-on: ${{ matrix.combination.runner }}
    timeout-minutes: 4
    strategy:
      matrix:
        combination:
          - tag: latest-amd64
            platform: 'linux/amd64'
            runner: ubuntu-22.04
          - tag: latest
            platform: ''
            runner: ubuntu-22.04
          - tag: latest-arm64
            platform: ''
            runner: besu-arm64
          - tag: latest
            platform: ''
            runner: besu-arm64

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          sparse-checkout: '.github/workflows/BesuContainerVerify.sh'

      - name: Start container
        run: |
          PLATFORM_OPT=""
          [[ x${{ matrix.combination.platform }} != 'x' ]] && PLATFORM_OPT="--platform ${{ matrix.combination.platform }}"
          docker run -d $PLATFORM_OPT --name ${{ env.CONTAINER_NAME }} ${{ secrets.DOCKER_ORG }}/besu:${{ matrix.combination.tag }}

      - name: Verify besu container
        run: bash .github/workflows/BesuContainerVerify.sh
        env:
          TAG: ${{ matrix.combination.tag }}
          VERSION: ${{ env.RELEASE_VERSION }}
          CHECK_LATEST: true

      - name: Stop container
        run: docker stop ${{ env.CONTAINER_NAME }}
