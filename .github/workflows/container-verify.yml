name: container verify

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Besu version'
        required: true
      verify-latest-version:
        description: 'Check latest container version'
        required: false
        type: choice
        default: "true"
        options:
          - "true"
          - "false"

jobs:
  verify:
    timeout-minutes: 4
    strategy:
      matrix:
        combination:
          - tag: ${{ inputs.version }}
            platform: ''
            runner: ubuntu-latest
          - tag: ${{ inputs.version }}-amd64
            platform: 'linux/amd64'
            runner: ubuntu-latest
          - tag: latest
            platform: ''
            runner: ubuntu-latest
          - tag: ${{ inputs.version }}-arm64
            platform: ''
            runner: besu-arm64
    runs-on: ${{ matrix.combination.runner }}
    env:
      CONTAINER_NAME: besu-check
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Start container
        run: |
          PLATFORM_OPT=""
          [[ x${{ matrix.combination.platform }} != 'x' ]] && PLATFORM_OPT="--platform ${{ matrix.combination.platform }}"
          docker run -d $PLATFORM_OPT --name ${{ env.CONTAINER_NAME }} hyperledger/besu:${{ matrix.combination.tag }}

      - name: Verify besu container
        run: bash .github/workflows/BesuContainerVerify.sh
        env:
          TAG: ${{ matrix.combination.tag }}
          VERSION: ${{ inputs.version }}
          CHECK_LATEST: ${{ inputs.verify-latest-version }}

      - name: Stop container
        run: docker stop ${{ env.CONTAINER_NAME }}
