name: <PERSON>O

on: [pull_request]

jobs:
  dco_check:
    runs-on: ubuntu-latest
    name: <PERSON><PERSON>
    if: ${{ github.actor != 'dependabot[bot]' }}
    steps:
    - name: Get PR Commits
      id: 'get-pr-commits'
      uses: tim-actions/get-pr-commits@198af03565609bb4ed924d1260247b4881f09e7d
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    - name: DCO Check
      uses: tim-actions/dco@f2279e6e62d5a7d9115b0cb8e837b777b1b02e21
      with:
        commits: ${{ steps.get-pr-commits.outputs.commits }}
