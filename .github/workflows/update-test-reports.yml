name: update-test-reports

on:
  push:
    branches:
      - main

jobs:
  syncTestReports:
    if: github.repository == 'hyperledger/besu'
    runs-on: ubuntu-22.04
    steps:
      - name: Get latest merge PR number
        id: latest_merged_pr_number
        run: echo "PULL_REQUEST_NUMBER=$(gh pr list --repo hyperledger/besu --base main --state merged --json "number,mergedAt" --search "sort:updated-desc" --jq 'max_by(.mergedAt)|.number')" >> "$GITHUB_OUTPUT"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Get unit test reports from latest merged PR
        uses: dawidd6/action-download-artifact@e7466d1a7587ed14867642c2ca74b5bcc1e19a2d
        with:
          workflow: pre-review.yml
          workflow_conclusion: success
          pr: ${{ env.LATEST_MERGED_PR_NUMBER }}
          name_is_regexp: true
          name: 'unit-\d+-test-results'
          path: unit-test-results
          if_no_artifact_found: fail
        env:
          LATEST_MERGED_PR_NUMBER: ${{ steps.latest_merged_pr_number.outputs.PULL_REQUEST_NUMBER }}
      - name: Upload unit test results
        uses: actions/upload-artifact@5d5d22a31266ced268874388b861e4b58bb5c2f3
        with:
          name: unit-test-results
          path: 'unit-test-results/**/test-results/**/TEST-*.xml'
      - name: Get acceptance test reports from latest merged PR
        uses: dawidd6/action-download-artifact@e7466d1a7587ed14867642c2ca74b5bcc1e19a2d
        with:
          workflow: acceptance-tests.yml
          workflow_conclusion: success
          pr: ${{ env.LATEST_MERGED_PR_NUMBER }}
          name_is_regexp: true
          name: 'acceptance-node-\d+-test-results'
          path: acceptance-test-results
          if_no_artifact_found: fail
        env:
          LATEST_MERGED_PR_NUMBER: ${{ steps.latest_merged_pr_number.outputs.PULL_REQUEST_NUMBER }}
      - name: Upload acceptance test results
        uses: actions/upload-artifact@5d5d22a31266ced268874388b861e4b58bb5c2f3
        with:
          name: acceptance-test-results
          path: 'acceptance-test-results/**/TEST-*.xml'
