name: container security scan

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Container image tag'
        required: false
        default: 'develop'      
  schedule:
    # Start of the hour is the busy time. Schedule it to run 8:17am UTC
    - cron:  '17 8 * * *'

jobs:
  scan-sarif:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
 
      # Shell parameter expansion does not support directly on a step
      # Adding a separate step to set the image tag. This allows running
      # this workflow with a schedule as well as manual
      - name: Set image tag
        id: tag
        run: |
          echo "TAG=${INPUT_TAG:-develop}" >> "$GITHUB_OUTPUT"
        env:
          INPUT_TAG: ${{ inputs.tag }} 

      - name: Vulnerability scanner
        id: trivy
        uses: aquasecurity/trivy-action@595be6a0f6560a0a8fc419ddf630567fc623531d
        with:
          image-ref: hyperledger/besu:${{ steps.tag.outputs.TAG }}
          format: sarif
          output: 'trivy-results.sarif'

      # Check the vulnerabilities via GitHub security tab
      - name: Upload results
        uses: github/codeql-action/upload-sarif@23acc5c183826b7a8a97bce3cecc52db901f8251
        with:
          sarif_file: 'trivy-results.sarif'
