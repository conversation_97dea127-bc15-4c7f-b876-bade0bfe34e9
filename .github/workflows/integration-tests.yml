name: integration-tests
on:
  workflow_dispatch:
  merge_group:
  pull_request:
    branches:
      - main
      - release-*
      - verkle
      - performance

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  GRADLE_OPTS: "-Xmx6g -Dorg.gradle.daemon=false -Dorg.gradle.parallel=true -Dorg.gradle.caching=true"

jobs:
  integration-tests:
    name: "integration-passed"
    runs-on: ubuntu-22.04
    permissions:
      statuses: write
      checks: write
    steps:
      - name: Checkout Repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          ref: ${{ github.event.pull_request.head.sha || github.ref }}
      - name: Set up Java
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00
        with:
          distribution: temurin
          java-version: 21
      - name: setup gradle
        uses: gradle/actions/setup-gradle@9e899d11ad247ec76be7a60bc1cf9d3abbb9e7f1
        with:
          cache-disabled: true
      - name: run integration tests
        run: ./gradlew integrationTest compileJmh


