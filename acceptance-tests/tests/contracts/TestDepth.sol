/*
 * Copyright contributors to Hyperledger Besu.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
pragma solidity >=0.7.0 <0.9.0;

contract TestDepth {
    uint256 public x;

    function depth(uint256 y) public {
        // bool result;
        if (y > 0) {
            // Store the current value of y in x during each iteration to ensure we do a SSTORE
            x = y;

            bytes memory call = abi.encodeWithSignature("depth(uint256)", --y);
            (bool result,) = address(this).delegatecall(call);
            require(result);
        }
        else {
            // Save the remaining gas in storage so that we can access it later
            x = gasleft();
        }
    }
}
