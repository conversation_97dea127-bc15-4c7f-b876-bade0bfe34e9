{"request": {"jsonrpc": "2.0", "method": "engine_forkchoiceUpdatedV1", "params": [{"headBlockHash": "0x3b8fb240d288781d4aac94d3fd16809ee413bc99294a085798a589dae51ddd4a", "safeBlockHash": "0x3b8fb240d288781d4aac94d3fd16809ee413bc99294a085798a589dae51ddd4a", "finalizedBlockHash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, {"timestamp": "0x5", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "suggestedFeeRecipient": "******************************************"}], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"payloadStatus": {"status": "VALID", "latestValidHash": "0x3b8fb240d288781d4aac94d3fd16809ee413bc99294a085798a589dae51ddd4a", "validationError": null}, "payloadId": "0x0065bd195a9b3bfb"}}, "statusCode": 200}