{"request": {"jsonrpc": "2.0", "method": "engine_newPayloadV1", "params": [{"parentHash": "0x3559e851470f6e7bbed1db474980683e8c315bfce99b2a6ef47c057c04de7858", "feeRecipient": "******************************************", "stateRoot": "0xca3149fa9e37db08d1cd49c9061db1002ef1cd58db2210f2115c8c989b2bdf45", "receiptsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "blockNumber": "0x2", "gasLimit": "0x1c9c380", "gasUsed": "0x0", "timestamp": "0x6", "extraData": "0x", "baseFeePerGas": "0x7", "blockHash": "0x80732341439dd124df94271386141735f13ef8a85e12e9fba15c06bc1ad9fd73", "transactions": []}], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"status": "VALID", "latestValidHash": "0x80732341439dd124df94271386141735f13ef8a85e12e9fba15c06bc1ad9fd73", "validationError": null}}, "statusCode": 200}