{"request": {"jsonrpc": "2.0", "method": "engine_forkchoiceUpdatedV1", "params": [{"headBlockHash": "0x80732341439dd124df94271386141735f13ef8a85e12e9fba15c06bc1ad9fd73", "safeBlockHash": "0x80732341439dd124df94271386141735f13ef8a85e12e9fba15c06bc1ad9fd73", "finalizedBlockHash": "0x3b8fb240d288781d4aac94d3fd16809ee413bc99294a085798a589dae51ddd4a"}, null], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"payloadStatus": {"status": "VALID", "latestValidHash": "0x80732341439dd124df94271386141735f13ef8a85e12e9fba15c06bc1ad9fd73", "validationError": null}, "payloadId": null}}, "statusCode": 200}