{"request": {"jsonrpc": "2.0", "method": "engine_newPayloadV4", "params": [{"parentHash": "0x27a2bc2ac21b3fc796f636bec1ec9cba100435f9a793176a83a5d4fa7cc13006", "feeRecipient": "******************************************", "stateRoot": "0x9b8c4a9a86cb49252075c0db2f0e72fb1e49350a0f70ea36f26f700201961e62", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "gasLimit": "0x1c9c380", "gasUsed": "0x0", "timestamp": "0x20", "extraData": "0x", "baseFeePerGas": "0x7", "excessBlobGas": "0x0", "transactions": [], "withdrawals": [], "blockNumber": "0x2", "blockHash": "0x2331b2dc9c453e9a33685099742cbbcd529d42bd5681969f45754f06866c6766", "receiptsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421", "blobGasUsed": "0x0"}, [], "0x0000000000000000000000000000000000000000000000000000000000000000", null], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "error": {"code": -32602, "message": "Invalid execution requests params", "data": "Missing execution requests field"}}, "statusCode": 200}