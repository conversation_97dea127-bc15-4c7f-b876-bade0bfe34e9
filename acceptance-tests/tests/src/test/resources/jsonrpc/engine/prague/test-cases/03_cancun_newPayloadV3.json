{"request": {"jsonrpc": "2.0", "method": "engine_newPayloadV3", "params": [{"parentHash": "0x01f5cbf33268c161f1526d704268db760bf82c9772a8f8ca412e0c6ce5684896", "feeRecipient": "******************************************", "stateRoot": "0x860be6ab5a8fc2003c3739bfe2cdbcd9dbb273c8ea42951b832a8e1f22fb3a60", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "gasLimit": "0x1c9c380", "gasUsed": "0x0", "timestamp": "0x10", "extraData": "0x", "baseFeePerGas": "0x7", "excessBlobGas": "0x0", "transactions": [], "withdrawals": [], "blockNumber": "0x1", "blobGasUsed": "0x0", "blockHash": "0x7cccf6d9ce3e5acaeac9058959c27ace53af3a30b15763e1703bab2d0ae9438e", "receiptsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"}, [], "0x0000000000000000000000000000000000000000000000000000000000000000"], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"status": "VALID", "latestValidHash": "0x7cccf6d9ce3e5acaeac9058959c27ace53af3a30b15763e1703bab2d0ae9438e", "validationError": null}}, "statusCode": 200}