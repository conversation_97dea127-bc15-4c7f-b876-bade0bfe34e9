{"request": {"jsonrpc": "2.0", "method": "engine_forkchoiceUpdatedV2", "params": [{"headBlockHash": "0xf4a1d287dd3bb7e877c57476912e6a6052bc4eed8ea70d032b55d77f26ee985f", "safeBlockHash": "0xf4a1d287dd3bb7e877c57476912e6a6052bc4eed8ea70d032b55d77f26ee985f", "finalizedBlockHash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, {"timestamp": "0x10", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "suggestedFeeRecipient": "******************************************", "withdrawals": [{"index": "0x0", "validatorIndex": "0x0", "address": "******************************************", "amount": "0x1"}, {"index": "0x1", "validatorIndex": "0x1", "address": "******************************************", "amount": "0x2"}]}], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"payloadStatus": {"status": "VALID", "latestValidHash": "0xf4a1d287dd3bb7e877c57476912e6a6052bc4eed8ea70d032b55d77f26ee985f", "validationError": null}, "payloadId": "0x0065bd2db6663ed9"}}, "statusCode": 200}