{"request": {"jsonrpc": "2.0", "method": "engine_newPayloadV2", "params": [{"parentHash": "0xf4a1d287dd3bb7e877c57476912e6a6052bc4eed8ea70d032b55d77f26ee985f", "feeRecipient": "******************************************", "stateRoot": "0xa61c2a422a4f7d7d7f456c1a83d5484eaf0d49e2b6b6d5716f875e782c66a9f0", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "gasLimit": "0x1c9c380", "gasUsed": "0x0", "timestamp": "0x10", "extraData": "0x", "baseFeePerGas": "0x7", "transactions": [], "withdrawals": [{"index": "0x0", "validatorIndex": "0x0", "address": "******************************************", "amount": "0x1"}, {"index": "0x1", "validatorIndex": "0x1", "address": "0xfe3b557e8fb62b89f4916b721be55ceb828dbd73", "amount": "0x2"}], "blockNumber": "0x2", "blockHash": "0x612abd8615f544759d4aeb3dbab32f5f198a8b818e9c5436e9f7a674ef3b0f20", "receiptsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"}], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"status": "VALID", "latestValidHash": "0x612abd8615f544759d4aeb3dbab32f5f198a8b818e9c5436e9f7a674ef3b0f20", "validationError": null}}, "statusCode": 200}