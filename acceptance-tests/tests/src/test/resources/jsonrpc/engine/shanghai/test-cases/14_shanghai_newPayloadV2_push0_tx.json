{"request": {"jsonrpc": "2.0", "method": "engine_newPayloadV2", "params": [{"parentHash": "0x612abd8615f544759d4aeb3dbab32f5f198a8b818e9c5436e9f7a674ef3b0f20", "feeRecipient": "******************************************", "stateRoot": "0xf8e89cc49d51d0bbbfd75fe214712e04f3b70e7c447261a2290262a8e1c4d0e1", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "gasLimit": "0x1c9c380", "gasUsed": "0x111f8", "timestamp": "0x11", "extraData": "0x", "baseFeePerGas": "0x7", "transactions": ["0xf874800a830aae6094b94f5374fce5edbc8e2a8697c15331677e6ebf0b809400000000000000000000000000000000000001001ba040c30479d1f1f7ac729145a6e98ff0bc4882cc21930cc283db84f42f1852d9fba0567ecfa0b8daeedfd9ab8d520bcf44f06d682be47dac7d64a6865cec4ec33cef"], "withdrawals": [], "blockNumber": "0x3", "blockHash": "0x85a4410f70b315e85382c7def115df2742348734f14c91bfbad0f2c67e9de4a2", "receiptsRoot": "0x1668c1a230c3e0aaa4c52af3bada02571dabb4e1743ebd6eaf4bbe8008e8cc3e"}], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"status": "VALID", "latestValidHash": "0x85a4410f70b315e85382c7def115df2742348734f14c91bfbad0f2c67e9de4a2", "validationError": null}}, "statusCode": 200}