{"request": {"jsonrpc": "2.0", "method": "engine_getPayloadV2", "params": ["0x0065bd2db6663ed9"], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"executionPayload": {"parentHash": "0xf4a1d287dd3bb7e877c57476912e6a6052bc4eed8ea70d032b55d77f26ee985f", "feeRecipient": "******************************************", "stateRoot": "0xa61c2a422a4f7d7d7f456c1a83d5484eaf0d49e2b6b6d5716f875e782c66a9f0", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "gasLimit": "0x1ca35ef", "gasUsed": "0x0", "timestamp": "0x10", "extraData": "0x", "baseFeePerGas": "0x7", "transactions": [], "withdrawals": [{"index": "0x0", "validatorIndex": "0x0", "address": "******************************************", "amount": "0x1"}, {"index": "0x1", "validatorIndex": "0x1", "address": "0xfe3b557e8fb62b89f4916b721be55ceb828dbd73", "amount": "0x2"}], "blockNumber": "0x2", "blockHash": "0xc7f79c3547adc7886a1607bfd1efb9de3d277991037dba01cffdd67e298aa2bf", "receiptsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"}, "blockValue": "0x0"}}, "statusCode": 200}