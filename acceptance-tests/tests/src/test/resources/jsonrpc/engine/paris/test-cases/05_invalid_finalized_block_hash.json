{"request": {"jsonrpc": "2.0", "method": "engine_forkchoiceUpdatedV1", "params": [{"headBlockHash": "0x3559e851470f6e7bbed1db474980683e8c315bfce99b2a6ef47c057c04de7858", "safeBlockHash": "0x3559e851470f6e7bbed1db474980683e8c315bfce99b2a6ef47c057c04de7858", "finalizedBlockHash": "0x00000040d288781d4aac94d3fd16809ee413bc99294a085798a589dae5abcdef"}, null], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "error": {"code": -38002, "message": "Invalid forkchoice state"}}, "statusCode": 200}