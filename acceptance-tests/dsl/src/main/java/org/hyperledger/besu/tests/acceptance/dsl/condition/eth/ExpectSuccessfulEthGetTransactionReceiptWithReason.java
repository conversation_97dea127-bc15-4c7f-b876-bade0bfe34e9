/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.tests.acceptance.dsl.condition.eth;

import static org.assertj.core.api.Assertions.assertThat;

import org.hyperledger.besu.tests.acceptance.dsl.WaitUtils;
import org.hyperledger.besu.tests.acceptance.dsl.condition.Condition;
import org.hyperledger.besu.tests.acceptance.dsl.node.Node;
import org.hyperledger.besu.tests.acceptance.dsl.transaction.eth.EthGetTransactionReceiptWithRevertReason;

import java.nio.charset.StandardCharsets;

import org.web3j.utils.Numeric;

public class ExpectSuccessfulEthGetTransactionReceiptWithReason implements Condition {

  private final EthGetTransactionReceiptWithRevertReason transaction;
  private final String expectedRevertReason;

  public ExpectSuccessfulEthGetTransactionReceiptWithReason(
      final EthGetTransactionReceiptWithRevertReason transaction,
      final String expectedRevertReason) {
    this.transaction = transaction;
    this.expectedRevertReason = expectedRevertReason;
  }

  @Override
  public void verify(final Node node) {
    WaitUtils.waitFor(() -> assertThat(revertReasonMatches(node, expectedRevertReason)).isTrue());
  }

  private boolean revertReasonMatches(final Node node, final String expectedRevertReason) {
    return node.execute(transaction)
        .filter(
            transactionReceipt -> {
              final byte[] bytes =
                  Numeric.hexStringToByteArray(transactionReceipt.getRevertReason());
              final String utf8Encoded = new String(bytes, StandardCharsets.UTF_8);
              return utf8Encoded.contains(expectedRevertReason);
            })
        .isPresent();
  }
}
